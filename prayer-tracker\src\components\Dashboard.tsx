import { useState, useEffect } from 'react';
import { ClockIcon } from '@heroicons/react/24/outline';
import { CheckCircleIcon as CheckCircleIconSolid } from '@heroicons/react/24/solid';
import { prayerTimesService, type PrayerTimesResponse } from '../services/prayerTimesService';

interface DashboardProps {
  prayerTimes: PrayerTimesResponse | null;
  theme: 'light' | 'dark';
  language: 'ar' | 'en';
  onPrayerToggle: (prayer: string, date: string, completed: boolean) => void;
}

interface PrayerStatus {
  fajr: boolean;
  dhuhr: boolean;
  asr: boolean;
  maghrib: boolean;
  isha: boolean;
}

export default function Dashboard({
  prayerTimes,
  theme,
  language,
  onPrayerToggle
}: DashboardProps) {
  const [todayPrayers, setTodayPrayers] = useState<PrayerStatus>({
    fajr: false,
    dhuhr: false,
    asr: false,
    maghrib: false,
    isha: false,
  });

  const [currentTime, setCurrentTime] = useState(new Date());
  const [nextPrayer, setNextPrayer] = useState<{
    name: string;
    time: string;
    timeLeft: string;
  } | null>(null);

  const isRTL = language === 'ar';

  // تحديث الوقت كل ثانية
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // تحميل حالة الصلوات من التخزين المحلي
  useEffect(() => {
    const today = new Date().toISOString().split('T')[0];
    const savedPrayers = localStorage.getItem(`prayers_${today}`);
    if (savedPrayers) {
      setTodayPrayers(JSON.parse(savedPrayers));
    }
  }, []);

  // حفظ حالة الصلوات في التخزين المحلي
  useEffect(() => {
    const today = new Date().toISOString().split('T')[0];
    localStorage.setItem(`prayers_${today}`, JSON.stringify(todayPrayers));
  }, [todayPrayers]);

  // حساب الصلاة القادمة
  useEffect(() => {
    if (!prayerTimes) return;

    const prayers = [
      { name: language === 'ar' ? 'الفجر' : 'Fajr', time: prayerTimes.fajir, key: 'fajr' },
      { name: language === 'ar' ? 'الظهر' : 'Dhuhr', time: prayerTimes.doher, key: 'dhuhr' },
      { name: language === 'ar' ? 'العصر' : 'Asr', time: prayerTimes.asr, key: 'asr' },
      { name: language === 'ar' ? 'المغرب' : 'Maghrib', time: prayerTimes.maghrib, key: 'maghrib' },
      { name: language === 'ar' ? 'العشاء' : 'Isha', time: prayerTimes.isha, key: 'isha' },
    ];

    const now = currentTime;
    const currentMinutes = now.getHours() * 60 + now.getMinutes();

    for (const prayer of prayers) {
      if (!prayer.time) continue;

      const [hours, minutes] = prayer.time.trim().split(':').map(Number);
      const prayerMinutes = hours * 60 + minutes;

      if (currentMinutes < prayerMinutes) {
        const timeLeftMinutes = prayerMinutes - currentMinutes;
        const hoursLeft = Math.floor(timeLeftMinutes / 60);
        const minutesLeft = timeLeftMinutes % 60;

        setNextPrayer({
          name: prayer.name,
          time: prayerTimesService.formatTimeForDisplay(prayer.time),
          timeLeft: hoursLeft > 0
            ? `${hoursLeft}:${minutesLeft.toString().padStart(2, '0')}`
            : `${minutesLeft} ${language === 'ar' ? 'دقيقة' : 'min'}`
        });
        return;
      }
    }

    // إذا انتهت جميع صلوات اليوم، الصلاة القادمة هي فجر الغد
    if (prayers[0].time) {
      setNextPrayer({
        name: prayers[0].name + (language === 'ar' ? ' (غداً)' : ' (Tomorrow)'),
        time: prayerTimesService.formatTimeForDisplay(prayers[0].time),
        timeLeft: language === 'ar' ? 'غداً' : 'Tomorrow'
      });
    }
  }, [prayerTimes, currentTime, language]);

  const handlePrayerToggle = (prayerKey: string) => {
    const newStatus = !todayPrayers[prayerKey as keyof PrayerStatus];
    setTodayPrayers(prev => ({
      ...prev,
      [prayerKey]: newStatus
    }));

    const today = new Date().toISOString().split('T')[0];
    onPrayerToggle(prayerKey, today, newStatus);
  };

  const completedCount = Object.values(todayPrayers).filter(Boolean).length;
  const completionRate = (completedCount / 5) * 100;

  const prayers = [
    {
      key: 'fajr',
      name: language === 'ar' ? 'الفجر' : 'Fajr',
      time: prayerTimes?.fajir || '5:18',
      icon: '🌅',
      color: 'from-blue-400 to-blue-600'
    },
    {
      key: 'dhuhr',
      name: language === 'ar' ? 'الظهر' : 'Dhuhr',
      time: prayerTimes?.doher || '11:53',
      icon: '🌞',
      color: 'from-yellow-400 to-yellow-600'
    },
    {
      key: 'asr',
      name: language === 'ar' ? 'العصر' : 'Asr',
      time: prayerTimes?.asr || '15:30',
      icon: '🌇',
      color: 'from-orange-400 to-orange-600'
    },
    {
      key: 'maghrib',
      name: language === 'ar' ? 'المغرب' : 'Maghrib',
      time: prayerTimes?.maghrib || '18:15',
      icon: '🌆',
      color: 'from-red-400 to-red-600'
    },
    {
      key: 'isha',
      name: language === 'ar' ? 'العشاء' : 'Isha',
      time: prayerTimes?.isha || '19:45',
      icon: '🌙',
      color: 'from-purple-400 to-purple-600'
    },
  ];

  return (
    <div className={`space-y-6 ${isRTL ? 'text-right' : 'text-left'}`}>
      {/* رأس لوحة التحكم */}
      <div className={`bg-gradient-to-r from-green-500 to-green-600 text-white rounded-2xl p-6 shadow-xl ${
        theme === 'dark' ? 'from-green-600 to-green-700' : ''
      }`}>
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold font-arabic">
              {language === 'ar' ? 'لوحة التحكم اليومية' : 'Daily Dashboard'}
            </h1>
            <p className="text-green-100 font-arabic">
              {currentTime.toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
            </p>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold">
              {currentTime.toLocaleTimeString(language === 'ar' ? 'ar-SA' : 'en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
              })}
            </div>
            <p className="text-green-100 text-sm font-arabic">
              {prayerTimes?.date || (language === 'ar' ? 'جاري التحميل...' : 'Loading...')}
            </p>
          </div>
        </div>

        {/* الصلاة القادمة */}
        {nextPrayer && (
          <div className="bg-white bg-opacity-10 rounded-xl p-4">
            <div className="flex items-center justify-between">
              <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                <ClockIcon className={`w-5 h-5 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                <span className="font-arabic">
                  {language === 'ar' ? 'الصلاة القادمة:' : 'Next Prayer:'}
                </span>
              </div>
              <div className={`text-right ${isRTL ? 'text-left' : ''}`}>
                <p className="font-bold font-arabic">{nextPrayer.name}</p>
                <p className="text-sm text-green-100">{nextPrayer.time}</p>
              </div>
            </div>
            <div className="mt-2 text-center">
              <p className="text-sm text-green-100 font-arabic">
                {language === 'ar' ? 'الوقت المتبقي:' : 'Time remaining:'} {nextPrayer.timeLeft}
              </p>
            </div>
          </div>
        )}
      </div>

      {/* شريط التقدم اليومي */}
      <div className={`bg-white rounded-2xl shadow-lg p-6 ${
        theme === 'dark' ? 'bg-gray-800 text-white' : ''
      }`}>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-bold font-arabic">
            {language === 'ar' ? 'التقدم اليومي' : 'Daily Progress'}
          </h2>
          <span className="text-lg font-bold text-green-600">
            {completedCount}/5 ({Math.round(completionRate)}%)
          </span>
        </div>

        <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
          <div
            className="bg-gradient-to-r from-green-500 to-green-600 h-3 rounded-full transition-all duration-500 ease-out"
            style={{ width: `${completionRate}%` }}
          />
        </div>

        {completionRate === 100 && (
          <div className="bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl p-4 text-center">
            <div className="text-3xl mb-2">🎉</div>
            <h3 className="text-lg font-bold font-arabic mb-1">
              {language === 'ar' ? 'أحسنت! أكملت جميع صلوات اليوم' : 'Excellent! You completed all prayers today'}
            </h3>
            <p className="text-sm font-arabic opacity-90">
              {language === 'ar'
                ? 'بارك الله فيك وتقبل منك صالح الأعمال'
                : 'May Allah bless you and accept your good deeds'
              }
            </p>
          </div>
        )}
      </div>

      {/* بطاقات الصلوات */}
      <div className="grid gap-4">
        {prayers.map((prayer) => {
          const isCompleted = todayPrayers[prayer.key as keyof PrayerStatus];

          return (
            <div
              key={prayer.key}
              onClick={() => handlePrayerToggle(prayer.key)}
              className={`relative overflow-hidden rounded-2xl shadow-lg border-r-4 transition-all duration-300 cursor-pointer transform hover:scale-105 active:scale-95 ${
                isCompleted
                  ? `bg-gradient-to-r ${prayer.color} text-white border-transparent shadow-xl`
                  : theme === 'dark'
                    ? 'bg-gray-800 text-white border-gray-600 hover:border-gray-500'
                    : 'bg-white border-gray-300 hover:border-gray-400'
              }`}
            >
              <div className="p-6">
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={`flex-1 ${isRTL ? 'text-right' : 'text-left'}`}>
                    <div className={`flex items-center mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className={`text-2xl ${isRTL ? 'ml-3' : 'mr-3'}`}>{prayer.icon}</span>
                      <h3 className="text-xl font-bold font-arabic">{prayer.name}</h3>
                    </div>
                    <p className={`text-sm font-arabic mb-2 ${
                      isCompleted ? 'text-white text-opacity-80' : 'text-gray-500'
                    }`}>
                      {language === 'ar' ? 'وقت الأذان' : 'Prayer Time'}
                    </p>
                    <p className="text-2xl font-bold">
                      {prayerTimesService.formatTimeForDisplay(prayer.time)}
                      {prayerTimes?.error && (
                        <span className="text-xs text-orange-500 mr-2" title={
                          language === 'ar' ? 'وقت تقريبي' : 'Approximate time'
                        }>~</span>
                      )}
                    </p>
                  </div>

                  <div className={`${isRTL ? 'mr-4' : 'ml-4'}`}>
                    {isCompleted ? (
                      <CheckCircleIconSolid className="w-12 h-12 text-white drop-shadow-lg" />
                    ) : (
                      <div className={`w-12 h-12 border-3 rounded-full flex items-center justify-center transition-all ${
                        theme === 'dark' ? 'border-gray-400' : 'border-gray-300'
                      }`}>
                        <div className={`w-6 h-6 border-2 border-current rounded-full ${
                          theme === 'dark' ? 'border-gray-400' : 'border-gray-400'
                        }`} />
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* تأثير الضوء عند الإكمال */}
              {isCompleted && (
                <div className="absolute inset-0 bg-white bg-opacity-10 animate-pulse" />
              )}
            </div>
          );
        })}
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-2 gap-4">
        {[
          {
            label: language === 'ar' ? 'اليوم' : 'Today',
            value: `${completedCount}/5`,
            color: 'text-blue-600',
            bg: 'bg-blue-50',
            icon: '📅'
          },
          {
            label: language === 'ar' ? 'هذا الأسبوع' : 'This Week',
            value: '28/35',
            color: 'text-green-600',
            bg: 'bg-green-50',
            icon: '📊'
          },
          {
            label: language === 'ar' ? 'هذا الشهر' : 'This Month',
            value: '142/155',
            color: 'text-purple-600',
            bg: 'bg-purple-50',
            icon: '📈'
          },
          {
            label: language === 'ar' ? 'المعدل' : 'Average',
            value: '91%',
            color: 'text-orange-600',
            bg: 'bg-orange-50',
            icon: '⭐'
          },
        ].map((stat) => (
          <div key={stat.label} className={`${stat.bg} rounded-xl p-4 text-center shadow-sm ${
            theme === 'dark' ? 'bg-gray-700' : ''
          }`}>
            <div className="text-2xl mb-2">{stat.icon}</div>
            <p className={`text-xs font-arabic mb-1 ${
              theme === 'dark' ? 'text-gray-300' : 'text-gray-600'
            }`}>
              {stat.label}
            </p>
            <p className={`text-lg font-bold ${theme === 'dark' ? 'text-white' : stat.color}`}>
              {stat.value}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
}
