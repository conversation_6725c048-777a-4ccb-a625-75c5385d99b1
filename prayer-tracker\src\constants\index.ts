// ===== ثوابت التطبيق =====
import { AppSettings, NotificationSettings, ProvinceData } from '../types';

// ===== أسماء الصلوات =====
export const PRAYER_NAMES = {
  ar: {
    fajr: 'الفجر',
    dhuhr: 'الظهر',
    asr: 'العصر',
    maghrib: 'المغرب',
    isha: 'العشاء',
  },
  en: {
    fajr: 'Fajr',
    dhuhr: 'Dhuhr',
    asr: 'Asr',
    maghrib: 'Maghrib',
    isha: 'Isha',
  },
} as const;

export const PRAYER_ORDER = ['fajr', 'dhuhr', 'asr', 'maghrib', 'isha'] as const;

// أعمار التكليف الافتراضية
export const TAKLIF_AGES = {
  male: {
    years: 14,
    months: 6,
    days: 15,
  },
  female: {
    years: 8,
    months: 8,
    days: 15,
  },
} as const;

// إعدادات API أوقات الصلاة
export const PRAYER_TIMES_API = {
  baseUrl: 'https://hq.alkafeel.net/Api/init/init.php',
  defaultParams: {
    v: 'jsonPrayerTimes',
    timezone: '+3',
    long: '44',
    lati: '32',
  },
} as const;

// المدن الرئيسية مع إحداثياتها
export const MAJOR_CITIES = {
  baghdad: { ar: 'بغداد', en: 'Baghdad', long: '44.4009', lati: '33.3152', timezone: '+3' },
  najaf: { ar: 'النجف', en: 'Najaf', long: '44.3449', lati: '32.0054', timezone: '+3' },
  karbala: { ar: 'كربلاء', en: 'Karbala', long: '44.0309', lati: '32.6109', timezone: '+3' },
  riyadh: { ar: 'الرياض', en: 'Riyadh', long: '46.7219', lati: '24.6877', timezone: '+3' },
  mecca: { ar: 'مكة', en: 'Mecca', long: '39.8579', lati: '21.3891', timezone: '+3' },
  medina: { ar: 'المدينة', en: 'Medina', long: '39.6178', lati: '24.5247', timezone: '+3' },
  kuwait: { ar: 'الكويت', en: 'Kuwait', long: '47.9734', lati: '29.3697', timezone: '+3' },
  doha: { ar: 'الدوحة', en: 'Doha', long: '51.5310', lati: '25.2867', timezone: '+3' },
  dubai: { ar: 'دبي', en: 'Dubai', long: '55.3047', lati: '25.2697', timezone: '+4' },
  beirut: { ar: 'بيروت', en: 'Beirut', long: '35.5018', lati: '33.8938', timezone: '+2' },
} as const;

// ألوان الحالات
export const STATUS_COLORS = {
  completed: '#15803d',
  incomplete: '#ef4444',
  beforeTaklif: '#9ca3af',
  currentDay: '#3b82f6',
  warning: '#f59e0b',
} as const;

// إعدادات الإشعارات الافتراضية
export const DEFAULT_NOTIFICATION_SETTINGS = {
  enabled: true,
  beforeMinutes: 10,
  sound: true,
} as const;

// إعدادات التقويم الافتراضية
export const DEFAULT_CALENDAR_SETTINGS = {
  primary: 'hijri' as const,
  showBoth: true,
  weekStart: 'saturday' as const,
} as const;

// إعدادات التطبيق الافتراضية
export const DEFAULT_APP_SETTINGS = {
  language: 'ar' as const,
  theme: 'light' as const,
  notifications: {
    enabled: true,
    beforeMinutes: 10,
    sound: true,
    prayers: {
      fajr: true,
      dhuhr: true,
      asr: true,
      maghrib: true,
      isha: true,
    },
  },
  location: {
    latitude: 33.3152,
    longitude: 44.4009,
    timezone: '+3',
    city: 'baghdad',
  },
  primaryCalendar: 'gregorian' as const,
  showBothCalendars: true,
} as const;

// مفاتيح التخزين المحلي
export const STORAGE_KEYS = {
  userData: 'prayer_tracker_user_data',
  dailyPrayers: 'prayer_tracker_daily_prayers',
  settings: 'prayer_tracker_settings',
  achievements: 'prayer_tracker_achievements',
  prayerTimes: 'prayer_tracker_prayer_times',
} as const;

// رسائل التحقق
export const VALIDATION_MESSAGES = {
  ar: {
    required: 'هذا الحقل مطلوب',
    invalidDate: 'تاريخ غير صحيح',
    futureBirthDate: 'تاريخ الميلاد لا يمكن أن يكون في المستقبل',
    invalidPrayerStartDate: 'تاريخ بداية الصلاة غير صحيح',
    prayerStartBeforeBirth: 'تاريخ بداية الصلاة لا يمكن أن يكون قبل تاريخ الميلاد',
  },
  en: {
    required: 'This field is required',
    invalidDate: 'Invalid date',
    futureBirthDate: 'Birth date cannot be in the future',
    invalidPrayerStartDate: 'Invalid prayer start date',
    prayerStartBeforeBirth: 'Prayer start date cannot be before birth date',
  },
} as const;

// إعدادات الإنجازات
export const ACHIEVEMENTS_CONFIG = [
  {
    id: 'first_day',
    type: 'daily' as const,
    requirement: 1,
    title: { ar: 'اليوم الأول', en: 'First Day' },
    description: { ar: 'أكمل جميع الصلوات في يوم واحد', en: 'Complete all prayers in one day' },
    icon: '🌟',
  },
  {
    id: 'week_streak',
    type: 'weekly' as const,
    requirement: 7,
    title: { ar: 'أسبوع كامل', en: 'Full Week' },
    description: { ar: 'أكمل جميع الصلوات لمدة أسبوع متتالي', en: 'Complete all prayers for a full week' },
    icon: '🏆',
  },
  {
    id: 'month_90_percent',
    type: 'monthly' as const,
    requirement: 90,
    title: { ar: 'شهر ممتاز', en: 'Excellent Month' },
    description: { ar: 'حقق نسبة 90% أو أكثر في شهر واحد', en: 'Achieve 90% or more in one month' },
    icon: '🎖️',
  },
  {
    id: 'hundred_days',
    type: 'special' as const,
    requirement: 100,
    title: { ar: 'مئة يوم', en: 'Hundred Days' },
    description: { ar: 'أكمل 100 يوم من الصلوات', en: 'Complete 100 days of prayers' },
    icon: '💯',
  },
] as const;

// أسماء الأشهر الهجرية
export const HIJRI_MONTHS = [
  { ar: 'محرم', en: 'Muharram' },
  { ar: 'صفر', en: 'Safar' },
  { ar: 'ربيع الأول', en: 'Rabi al-Awwal' },
  { ar: 'ربيع الثاني', en: 'Rabi al-Thani' },
  { ar: 'جمادى الأولى', en: 'Jumada al-Awwal' },
  { ar: 'جمادى الثانية', en: 'Jumada al-Thani' },
  { ar: 'رجب', en: 'Rajab' },
  { ar: 'شعبان', en: 'Shaban' },
  { ar: 'رمضان', en: 'Ramadan' },
  { ar: 'شوال', en: 'Shawwal' },
  { ar: 'ذو القعدة', en: 'Dhu al-Qadah' },
  { ar: 'ذو الحجة', en: 'Dhu al-Hijjah' },
] as const;

// أسماء الأشهر الميلادية
export const GREGORIAN_MONTHS = [
  { ar: 'يناير', en: 'January' },
  { ar: 'فبراير', en: 'February' },
  { ar: 'مارس', en: 'March' },
  { ar: 'أبريل', en: 'April' },
  { ar: 'مايو', en: 'May' },
  { ar: 'يونيو', en: 'June' },
  { ar: 'يوليو', en: 'July' },
  { ar: 'أغسطس', en: 'August' },
  { ar: 'سبتمبر', en: 'September' },
  { ar: 'أكتوبر', en: 'October' },
  { ar: 'نوفمبر', en: 'November' },
  { ar: 'ديسمبر', en: 'December' },
] as const;

// أسماء أيام الأسبوع
export const WEEK_DAYS = [
  { ar: 'السبت', en: 'Saturday' },
  { ar: 'الأحد', en: 'Sunday' },
  { ar: 'الاثنين', en: 'Monday' },
  { ar: 'الثلاثاء', en: 'Tuesday' },
  { ar: 'الأربعاء', en: 'Wednesday' },
  { ar: 'الخميس', en: 'Thursday' },
  { ar: 'الجمعة', en: 'Friday' },
] as const;
