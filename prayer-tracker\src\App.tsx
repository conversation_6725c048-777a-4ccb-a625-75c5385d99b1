import React from 'react';

function App() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-green-100 flex items-center justify-center">
      <div className="text-center p-8">
        <div className="mb-8">
          <h1 className="text-5xl font-bold text-green-800 mb-4">
            متتبع الصلوات
          </h1>
          <p className="text-xl text-green-600">
            تطبيق تتبع الصلوات الإلكتروني
          </p>
        </div>
        
        <div className="bg-white rounded-2xl shadow-xl p-8 max-w-md mx-auto">
          <div className="mb-6">
            <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl text-white">🕌</span>
            </div>
            <h2 className="text-2xl font-bold text-gray-800 mb-2">
              مرحباً بك
            </h2>
            <p className="text-gray-600">
              تطبيق شامل لتتبع الصلوات اليومية وإدارة العبادات
            </p>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center justify-center space-x-4 space-x-reverse">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-2">
                  <span className="text-xl">📊</span>
                </div>
                <p className="text-xs text-gray-600">تتبع التقدم</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-2">
                  <span className="text-xl">🏆</span>
                </div>
                <p className="text-xs text-gray-600">الإنجازات</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-2">
                  <span className="text-xl">⏰</span>
                </div>
                <p className="text-xs text-gray-600">أوقات الصلاة</p>
              </div>
            </div>
            
            <div className="mt-6">
              <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                <div className="bg-green-500 h-2 rounded-full animate-pulse" style={{width: '75%'}}></div>
              </div>
              <p className="text-sm text-gray-600">التطبيق جاهز للاستخدام!</p>
            </div>
          </div>
        </div>
        
        <div className="mt-8 text-center">
          <p className="text-sm text-green-700">
            "وأقيموا الصلاة وآتوا الزكاة واركعوا مع الراكعين"
          </p>
          <p className="text-xs text-green-600 mt-1">البقرة: 43</p>
        </div>
      </div>
    </div>
  );
}

export default App;
