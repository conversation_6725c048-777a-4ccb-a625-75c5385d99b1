import { useState, useEffect } from 'react';
import { ChevronLeftIcon, ChevronRightIcon, CalendarIcon } from '@heroicons/react/24/outline';

interface PrayerTableProps {
  userData: {
    fullName: string;
    gender: 'male' | 'female';
    birthDate: string;
    prayerStartDate: string;
    calculatedTaklefDate: string;
  };
  theme: 'light' | 'dark';
  language: 'ar' | 'en';
  primaryCalendar: 'hijri' | 'gregorian';
}

interface DayPrayers {
  fajr: boolean;
  dhuhr: boolean;
  asr: boolean;
  maghrib: boolean;
  isha: boolean;
}

interface MonthData {
  [day: string]: DayPrayers;
}

export default function PrayerTable({ userData, theme, language, primaryCalendar }: PrayerTableProps) {
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth());
  const [monthData, setMonthData] = useState<MonthData>({});


  const isRTL = language === 'ar';
  const prayerStartDate = new Date(userData.prayerStartDate);
  const taklefDate = new Date(userData.calculatedTaklefDate);
  const today = new Date();

  const monthNames = language === 'ar' 
    ? ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']
    : ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];

  const prayerNames = language === 'ar' 
    ? ['الفجر', 'الظهر', 'العصر', 'المغرب', 'العشاء']
    : ['Fajr', 'Dhuhr', 'Asr', 'Maghrib', 'Isha'];

  const dayNames = language === 'ar'
    ? ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت']
    : ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  // تحميل بيانات الشهر من التخزين المحلي
  useEffect(() => {
    const monthKey = `${selectedYear}-${selectedMonth.toString().padStart(2, '0')}`;
    const savedData = localStorage.getItem(`month_${monthKey}`);
    if (savedData) {
      setMonthData(JSON.parse(savedData));
    } else {
      setMonthData({});
    }
  }, [selectedYear, selectedMonth]);

  // حفظ بيانات الشهر في التخزين المحلي
  useEffect(() => {
    const monthKey = `${selectedYear}-${selectedMonth.toString().padStart(2, '0')}`;
    localStorage.setItem(`month_${monthKey}`, JSON.stringify(monthData));
  }, [monthData, selectedYear, selectedMonth]);

  // تحديد حالة اليوم (قبل التكليف، بعد بداية الصلاة، إلخ)
  const getDayStatus = (date: Date) => {
    if (date < taklefDate) return 'before-taklef';
    if (date < prayerStartDate) return 'before-start';
    if (date > today) return 'future';
    return 'trackable';
  };

  // تبديل حالة الصلاة
  const togglePrayer = (day: number, prayer: keyof DayPrayers) => {
    const date = new Date(selectedYear, selectedMonth, day);
    const status = getDayStatus(date);
    
    if (status !== 'trackable') return;

    const dayKey = day.toString().padStart(2, '0');
    setMonthData(prev => ({
      ...prev,
      [dayKey]: {
        ...prev[dayKey] || { fajr: false, dhuhr: false, asr: false, maghrib: false, isha: false },
        [prayer]: !prev[dayKey]?.[prayer]
      }
    }));
  };

  // الحصول على لون الخلية
  const getCellColor = (day: number, prayer: keyof DayPrayers) => {
    const date = new Date(selectedYear, selectedMonth, day);
    const status = getDayStatus(date);
    const dayKey = day.toString().padStart(2, '0');
    const isCompleted = monthData[dayKey]?.[prayer];

    switch (status) {
      case 'before-taklef':
        return theme === 'dark' ? 'bg-gray-600 text-gray-300' : 'bg-gray-300 text-gray-600';
      case 'before-start':
        return theme === 'dark' ? 'bg-green-700 text-green-200' : 'bg-green-200 text-green-800';
      case 'future':
        return theme === 'dark' ? 'bg-gray-700 text-gray-400' : 'bg-gray-100 text-gray-400';
      case 'trackable':
        if (isCompleted) {
          return 'bg-green-500 text-white';
        } else {
          return theme === 'dark' ? 'bg-red-700 text-red-200' : 'bg-red-100 text-red-800';
        }
      default:
        return theme === 'dark' ? 'bg-gray-700' : 'bg-gray-100';
    }
  };

  // حساب إحصائيات الشهر
  const getMonthStats = () => {
    const daysInMonth = new Date(selectedYear, selectedMonth + 1, 0).getDate();
    let totalPrayers = 0;
    let completedPrayers = 0;

    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(selectedYear, selectedMonth, day);
      const status = getDayStatus(date);
      
      if (status === 'trackable') {
        const dayKey = day.toString().padStart(2, '0');
        const dayPrayers = monthData[dayKey];
        
        totalPrayers += 5;
        if (dayPrayers) {
          completedPrayers += Object.values(dayPrayers).filter(Boolean).length;
        }
      }
    }

    return {
      total: totalPrayers,
      completed: completedPrayers,
      percentage: totalPrayers > 0 ? Math.round((completedPrayers / totalPrayers) * 100) : 0
    };
  };

  const stats = getMonthStats();
  const daysInMonth = new Date(selectedYear, selectedMonth + 1, 0).getDate();

  return (
    <div className={`space-y-6 ${isRTL ? 'text-right' : 'text-left'}`}>
      {/* رأس الصفحة */}
      <div className={`bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-2xl p-6 shadow-xl ${
        theme === 'dark' ? 'from-blue-600 to-blue-700' : ''
      }`}>
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold font-arabic">
              {language === 'ar' ? 'جدول الصلاة' : 'Prayer Table'}
            </h1>
            <p className="text-blue-100 font-arabic">
              {language === 'ar' ? 'السجل التاريخي للصلوات' : 'Historical Prayer Records'}
            </p>
          </div>
          <CalendarIcon className="w-12 h-12 text-blue-200" />
        </div>

        {/* إحصائيات الشهر */}
        <div className="bg-white bg-opacity-10 rounded-xl p-4">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <p className="text-2xl font-bold">{stats.completed}</p>
              <p className="text-blue-100 text-sm font-arabic">
                {language === 'ar' ? 'مكتملة' : 'Completed'}
              </p>
            </div>
            <div>
              <p className="text-2xl font-bold">{stats.total - stats.completed}</p>
              <p className="text-blue-100 text-sm font-arabic">
                {language === 'ar' ? 'غير مكتملة' : 'Missed'}
              </p>
            </div>
            <div>
              <p className="text-2xl font-bold">{stats.percentage}%</p>
              <p className="text-blue-100 text-sm font-arabic">
                {language === 'ar' ? 'النسبة' : 'Rate'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* أدوات التنقل */}
      <div className={`bg-white rounded-2xl shadow-lg p-4 ${
        theme === 'dark' ? 'bg-gray-800' : ''
      }`}>
        <div className="flex items-center justify-between">
          <button
            onClick={() => {
              if (selectedMonth === 0) {
                setSelectedMonth(11);
                setSelectedYear(selectedYear - 1);
              } else {
                setSelectedMonth(selectedMonth - 1);
              }
            }}
            className={`p-2 rounded-lg transition-colors ${
              theme === 'dark' 
                ? 'hover:bg-gray-700 text-gray-300' 
                : 'hover:bg-gray-100 text-gray-600'
            }`}
          >
            {isRTL ? <ChevronRightIcon className="w-5 h-5" /> : <ChevronLeftIcon className="w-5 h-5" />}
          </button>

          <div className="text-center">
            <h2 className="text-xl font-bold font-arabic">
              {monthNames[selectedMonth]} {selectedYear}
            </h2>
            {primaryCalendar === 'hijri' && (
              <p className={`text-sm font-arabic ${
                theme === 'dark' ? 'text-gray-400' : 'text-gray-600'
              }`}>
                {/* يمكن إضافة التاريخ الهجري هنا */}
                {selectedYear - 579} هـ
              </p>
            )}
          </div>

          <button
            onClick={() => {
              if (selectedMonth === 11) {
                setSelectedMonth(0);
                setSelectedYear(selectedYear + 1);
              } else {
                setSelectedMonth(selectedMonth + 1);
              }
            }}
            className={`p-2 rounded-lg transition-colors ${
              theme === 'dark' 
                ? 'hover:bg-gray-700 text-gray-300' 
                : 'hover:bg-gray-100 text-gray-600'
            }`}
          >
            {isRTL ? <ChevronLeftIcon className="w-5 h-5" /> : <ChevronRightIcon className="w-5 h-5" />}
          </button>
        </div>
      </div>

      {/* جدول الصلوات */}
      <div className={`bg-white rounded-2xl shadow-lg overflow-hidden ${
        theme === 'dark' ? 'bg-gray-800' : ''
      }`}>
        {/* رأس الجدول */}
        <div className={`bg-gray-50 p-4 border-b ${
          theme === 'dark' ? 'bg-gray-700 border-gray-600' : 'border-gray-200'
        }`}>
          <div className="grid grid-cols-6 gap-2 text-center">
            <div className={`font-bold font-arabic ${
              theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
            }`}>
              {language === 'ar' ? 'اليوم' : 'Day'}
            </div>
            {prayerNames.map((prayer) => (
              <div key={prayer} className={`font-bold font-arabic text-sm ${
                theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
              }`}>
                {prayer}
              </div>
            ))}
          </div>
        </div>

        {/* أيام الشهر */}
        <div className="p-4">
          <div className="space-y-2">
            {Array.from({ length: daysInMonth }, (_, i) => i + 1).map((day) => {
              const date = new Date(selectedYear, selectedMonth, day);
              const isToday = date.toDateString() === today.toDateString();
              const dayName = dayNames[date.getDay()];

              return (
                <div
                  key={day}
                  className={`grid grid-cols-6 gap-2 p-2 rounded-lg transition-colors ${
                    isToday 
                      ? theme === 'dark' 
                        ? 'bg-blue-900 bg-opacity-50' 
                        : 'bg-blue-50 border border-blue-200'
                      : theme === 'dark'
                        ? 'hover:bg-gray-700'
                        : 'hover:bg-gray-50'
                  }`}
                >
                  {/* عمود اليوم */}
                  <div className="text-center">
                    <div className={`font-bold ${
                      isToday 
                        ? 'text-blue-600' 
                        : theme === 'dark' 
                          ? 'text-gray-300' 
                          : 'text-gray-800'
                    }`}>
                      {day}
                    </div>
                    <div className={`text-xs font-arabic ${
                      theme === 'dark' ? 'text-gray-400' : 'text-gray-500'
                    }`}>
                      {dayName}
                    </div>
                  </div>

                  {/* أعمدة الصلوات */}
                  {(['fajr', 'dhuhr', 'asr', 'maghrib', 'isha'] as const).map((prayer) => (
                    <button
                      key={prayer}
                      onClick={() => togglePrayer(day, prayer)}
                      className={`h-12 rounded-lg transition-all duration-200 hover:scale-105 active:scale-95 ${
                        getCellColor(day, prayer)
                      }`}
                      disabled={getDayStatus(date) !== 'trackable'}
                    >
                      {monthData[day.toString().padStart(2, '0')]?.[prayer] && (
                        <span className="text-white">✓</span>
                      )}
                    </button>
                  ))}
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* مفتاح الألوان */}
      <div className={`bg-white rounded-2xl shadow-lg p-4 ${
        theme === 'dark' ? 'bg-gray-800' : ''
      }`}>
        <h3 className={`font-bold font-arabic mb-3 ${
          theme === 'dark' ? 'text-gray-300' : 'text-gray-800'
        }`}>
          {language === 'ar' ? 'مفتاح الألوان:' : 'Color Legend:'}
        </h3>
        <div className="grid grid-cols-2 gap-3 text-sm">
          <div className="flex items-center">
            <div className="w-4 h-4 bg-green-500 rounded mr-2"></div>
            <span className={`font-arabic ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
              {language === 'ar' ? 'صلاة مكتملة' : 'Completed Prayer'}
            </span>
          </div>
          <div className="flex items-center">
            <div className={`w-4 h-4 rounded mr-2 ${
              theme === 'dark' ? 'bg-red-700' : 'bg-red-100'
            }`}></div>
            <span className={`font-arabic ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
              {language === 'ar' ? 'صلاة غير مكتملة' : 'Missed Prayer'}
            </span>
          </div>
          <div className="flex items-center">
            <div className={`w-4 h-4 rounded mr-2 ${
              theme === 'dark' ? 'bg-gray-600' : 'bg-gray-300'
            }`}></div>
            <span className={`font-arabic ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
              {language === 'ar' ? 'قبل سن التكليف' : 'Before Taklef Age'}
            </span>
          </div>
          <div className="flex items-center">
            <div className={`w-4 h-4 rounded mr-2 ${
              theme === 'dark' ? 'bg-green-700' : 'bg-green-200'
            }`}></div>
            <span className={`font-arabic ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
              {language === 'ar' ? 'مكتملة تلقائياً' : 'Auto Completed'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
