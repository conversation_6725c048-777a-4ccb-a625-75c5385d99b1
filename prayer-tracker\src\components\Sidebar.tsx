import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  HomeIcon,
  CalendarDaysIcon,
  ChartBarIcon,
  Cog6ToothIcon,
  Bars3Icon,
  XMarkIcon,
  UserIcon,
  BellIcon,
  SunIcon,
  MoonIcon
} from '@heroicons/react/24/outline';
import {
  HomeIcon as HomeIconSolid,
  CalendarDaysIcon as CalendarDaysIconSolid,
  ChartBarIcon as ChartBarIconSolid,
  Cog6ToothIcon as Cog6ToothIconSolid
} from '@heroicons/react/24/solid';

interface SidebarProps {
  isOpen: boolean;
  onToggle: () => void;
  userData?: {
    fullName: string;
    gender: 'male' | 'female';
  };
  theme: 'light' | 'dark';
  onThemeToggle: () => void;
  language: 'ar' | 'en';
}

export default function Sidebar({ 
  isOpen, 
  onToggle, 
  userData, 
  theme, 
  onThemeToggle,
  language 
}: SidebarProps) {
  const location = useLocation();
  const isRTL = language === 'ar';

  const navigationItems = [
    {
      name: language === 'ar' ? 'الصفحة الرئيسية' : 'Dashboard',
      href: '/',
      icon: HomeIcon,
      iconSolid: HomeIconSolid,
      color: 'from-green-400 to-green-600',
      description: language === 'ar' ? 'تتبع الصلوات اليومية' : 'Daily prayer tracking'
    },
    {
      name: language === 'ar' ? 'جدول الصلاة' : 'Prayer Table',
      href: '/prayer-table',
      icon: CalendarDaysIcon,
      iconSolid: CalendarDaysIconSolid,
      color: 'from-blue-400 to-blue-600',
      description: language === 'ar' ? 'السجل التاريخي للصلوات' : 'Historical prayer records'
    },
    {
      name: language === 'ar' ? 'التقدم والإنجازات' : 'Progress & Achievements',
      href: '/progress',
      icon: ChartBarIcon,
      iconSolid: ChartBarIconSolid,
      color: 'from-purple-400 to-purple-600',
      description: language === 'ar' ? 'الإحصائيات والإنجازات' : 'Statistics and achievements'
    },
    {
      name: language === 'ar' ? 'الإعدادات' : 'Settings',
      href: '/settings',
      icon: Cog6ToothIcon,
      iconSolid: Cog6ToothIconSolid,
      color: 'from-gray-400 to-gray-600',
      description: language === 'ar' ? 'إعدادات التطبيق' : 'Application settings'
    }
  ];

  const isActive = (href: string) => {
    if (href === '/') {
      return location.pathname === '/' || location.pathname === '/dashboard';
    }
    return location.pathname === href;
  };

  return (
    <>
      {/* زر فتح الشريط الجانبي */}
      <button
        onClick={onToggle}
        className={`fixed top-4 z-50 p-3 bg-white shadow-lg rounded-full hover:shadow-xl transition-all duration-300 ${
          isRTL ? 'right-4' : 'left-4'
        } ${isOpen ? 'opacity-0 pointer-events-none' : 'opacity-100'}`}
      >
        <Bars3Icon className="w-6 h-6 text-gray-700" />
      </button>

      {/* خلفية مظلمة */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity duration-300"
          onClick={onToggle}
        />
      )}

      {/* الشريط الجانبي */}
      <div
        className={`fixed top-0 h-full w-80 bg-white shadow-2xl z-50 transform transition-transform duration-300 ease-in-out ${
          isRTL ? 'right-0' : 'left-0'
        } ${
          isOpen 
            ? 'translate-x-0' 
            : isRTL 
              ? 'translate-x-full' 
              : '-translate-x-full'
        } ${theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-white text-gray-900'}`}
      >
        {/* رأس الشريط الجانبي */}
        <div className={`p-6 bg-gradient-to-r from-green-500 to-green-600 text-white ${isRTL ? 'text-right' : 'text-left'}`}>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center ml-3">
                <span className="text-lg">🕌</span>
              </div>
              <div>
                <h1 className="text-lg font-bold font-arabic">
                  {language === 'ar' ? 'متتبع الصلوات' : 'Prayer Tracker'}
                </h1>
                <p className="text-xs text-green-100 font-arabic">
                  {language === 'ar' ? 'تطبيق تتبع الصلوات الإلكتروني' : 'Electronic Prayer Tracking App'}
                </p>
              </div>
            </div>
            <button
              onClick={onToggle}
              className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>

          {/* معلومات المستخدم */}
          {userData && (
            <div className="flex items-center p-3 bg-white bg-opacity-10 rounded-lg">
              <div className="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center ml-3">
                <UserIcon className="w-6 h-6" />
              </div>
              <div>
                <p className="font-medium font-arabic">{userData.fullName}</p>
                <p className="text-xs text-green-100 font-arabic">
                  {userData.gender === 'male' 
                    ? (language === 'ar' ? 'ذكر' : 'Male')
                    : (language === 'ar' ? 'أنثى' : 'Female')
                  }
                </p>
              </div>
            </div>
          )}
        </div>

        {/* قائمة التنقل */}
        <div className="flex-1 p-4 space-y-2">
          {navigationItems.map((item) => {
            const active = isActive(item.href);
            const Icon = active ? item.iconSolid : item.icon;

            return (
              <Link
                key={item.href}
                to={item.href}
                onClick={onToggle}
                className={`group flex items-center p-4 rounded-xl transition-all duration-200 ${
                  active
                    ? `bg-gradient-to-r ${item.color} text-white shadow-lg transform scale-105`
                    : theme === 'dark'
                      ? 'hover:bg-gray-800 text-gray-300 hover:text-white'
                      : 'hover:bg-gray-50 text-gray-700 hover:text-gray-900'
                } ${isRTL ? 'text-right' : 'text-left'}`}
              >
                <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''} w-full`}>
                  <div className={`p-2 rounded-lg ${
                    active 
                      ? 'bg-white bg-opacity-20' 
                      : theme === 'dark'
                        ? 'bg-gray-700 group-hover:bg-gray-600'
                        : 'bg-gray-100 group-hover:bg-gray-200'
                  } ${isRTL ? 'ml-4' : 'mr-4'}`}>
                    <Icon className="w-5 h-5" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium font-arabic">{item.name}</h3>
                    <p className={`text-xs font-arabic ${
                      active 
                        ? 'text-white text-opacity-80' 
                        : theme === 'dark'
                          ? 'text-gray-400'
                          : 'text-gray-500'
                    }`}>
                      {item.description}
                    </p>
                  </div>
                  {active && (
                    <div className={`w-2 h-2 bg-white rounded-full animate-pulse ${isRTL ? 'mr-2' : 'ml-2'}`} />
                  )}
                </div>
              </Link>
            );
          })}
        </div>

        {/* أدوات سريعة */}
        <div className={`p-4 border-t ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}`}>
          <h4 className={`text-sm font-medium font-arabic mb-3 ${
            theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
          } ${isRTL ? 'text-right' : 'text-left'}`}>
            {language === 'ar' ? 'أدوات سريعة' : 'Quick Tools'}
          </h4>
          
          <div className="space-y-2">
            {/* تبديل المظهر */}
            <button
              onClick={onThemeToggle}
              className={`w-full flex items-center p-3 rounded-lg transition-colors ${
                theme === 'dark'
                  ? 'hover:bg-gray-800 text-gray-300'
                  : 'hover:bg-gray-50 text-gray-700'
              } ${isRTL ? 'flex-row-reverse text-right' : 'text-left'}`}
            >
              <div className={`p-2 rounded-lg ${
                theme === 'dark' ? 'bg-gray-700' : 'bg-gray-100'
              } ${isRTL ? 'ml-3' : 'mr-3'}`}>
                {theme === 'dark' ? (
                  <SunIcon className="w-4 h-4" />
                ) : (
                  <MoonIcon className="w-4 h-4" />
                )}
              </div>
              <span className="text-sm font-arabic">
                {theme === 'dark' 
                  ? (language === 'ar' ? 'المظهر الفاتح' : 'Light Mode')
                  : (language === 'ar' ? 'المظهر المظلم' : 'Dark Mode')
                }
              </span>
            </button>

            {/* الإشعارات */}
            <button
              className={`w-full flex items-center p-3 rounded-lg transition-colors ${
                theme === 'dark'
                  ? 'hover:bg-gray-800 text-gray-300'
                  : 'hover:bg-gray-50 text-gray-700'
              } ${isRTL ? 'flex-row-reverse text-right' : 'text-left'}`}
            >
              <div className={`p-2 rounded-lg ${
                theme === 'dark' ? 'bg-gray-700' : 'bg-gray-100'
              } ${isRTL ? 'ml-3' : 'mr-3'}`}>
                <BellIcon className="w-4 h-4" />
              </div>
              <span className="text-sm font-arabic">
                {language === 'ar' ? 'الإشعارات' : 'Notifications'}
              </span>
            </button>
          </div>
        </div>

        {/* معلومات التطبيق */}
        <div className={`p-4 border-t ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className={`text-center ${isRTL ? 'text-right' : 'text-left'}`}>
            <p className={`text-xs font-arabic ${
              theme === 'dark' ? 'text-gray-400' : 'text-gray-500'
            }`}>
              {language === 'ar' ? 'الإصدار' : 'Version'} 1.0.0
            </p>
            <p className={`text-xs font-arabic ${
              theme === 'dark' ? 'text-gray-400' : 'text-gray-500'
            }`}>
              {language === 'ar' ? 'مصدر أوقات الصلاة: شبكة الكفيل' : 'Prayer times: Al-Kafeel Network'}
            </p>
          </div>
        </div>
      </div>
    </>
  );
}
