# 📁 هيكل مشروع متتبع الصلوات

## 🗂️ التنظيم العام للملفات

```
prayer-tracker/
├── 📁 src/
│   ├── 📁 components/          # المكونات
│   │   ├── 📁 ui/             # مكونات واجهة المستخدم الأساسية
│   │   ├── 📁 layout/         # مكونات التخطيط
│   │   ├── 📁 pages/          # مكونات الصفحات
│   │   ├── Dashboard.tsx      # لوحة التحكم الرئيسية
│   │   ├── InitialSetup.tsx   # الإعداد الأولي
│   │   ├── PrayerTable.tsx    # جدول الصلوات
│   │   ├── Progress.tsx       # صفحة التقدم والإنجازات
│   │   ├── Settings.tsx       # صفحة الإعدادات
│   │   └── Sidebar.tsx        # الشريط الجانبي
│   │
│   ├── 📁 types/              # تعريفات الأنواع
│   │   └── index.ts           # جميع أنواع البيانات
│   │
│   ├── 📁 constants/          # الثوابت والإعدادات
│   │   └── index.ts           # الثوابت العامة
│   │
│   ├── 📁 utils/              # الأدوات المساعدة
│   │   └── index.ts           # دوال مساعدة عامة
│   │
│   ├── 📁 services/           # الخدمات والAPI
│   │   └── prayerTimesService.ts  # خدمة أوقات الصلاة
│   │
│   ├── 📁 hooks/              # React Hooks مخصصة
│   │   └── (قيد الإنشاء)
│   │
│   ├── 📁 styles/             # ملفات التنسيق
│   │   └── (قيد الإنشاء)
│   │
│   ├── 📁 data/               # البيانات الثابتة
│   │   └── (قيد الإنشاء)
│   │
│   ├── 📁 assets/             # الموارد (صور، أيقونات)
│   │   └── react.svg
│   │
│   ├── App.tsx                # المكون الرئيسي
│   ├── main.tsx               # نقطة دخول التطبيق
│   ├── index.css              # التنسيقات الأساسية
│   └── vite-env.d.ts          # تعريفات TypeScript
│
├── 📁 public/                 # الملفات العامة
├── 📁 node_modules/           # حزم Node.js
├── package.json               # إعدادات المشروع
├── tsconfig.json              # إعدادات TypeScript
├── tailwind.config.js         # إعدادات Tailwind CSS
├── vite.config.ts             # إعدادات Vite
└── README.md                  # وثائق المشروع
```

## 📋 وصف المجلدات

### 🧩 `src/components/`
يحتوي على جميع مكونات React المقسمة إلى فئات:

- **`ui/`**: مكونات واجهة المستخدم الأساسية (أزرار، نماذج، إلخ)
- **`layout/`**: مكونات التخطيط (رأس الصفحة، تذييل، إلخ)
- **`pages/`**: مكونات الصفحات الكاملة
- **الملفات الرئيسية**: المكونات الأساسية للتطبيق

### 🏷️ `src/types/`
تعريفات جميع أنواع البيانات المستخدمة في التطبيق:
- `UserData`: بيانات المستخدم
- `AppSettings`: إعدادات التطبيق
- `PrayerTimesResponse`: استجابة أوقات الصلاة
- `Achievement`: الإنجازات
- وغيرها...

### ⚙️ `src/constants/`
الثوابت والإعدادات الافتراضية:
- أسماء الصلوات
- محافظات العراق
- إعدادات API
- رسائل النجاح والأخطاء
- ألوان التطبيق

### 🛠️ `src/utils/`
الأدوات المساعدة والدوال العامة:
- دوال التاريخ والوقت
- دوال التخزين المحلي
- دوال التحقق من صحة البيانات
- دوال الحسابات
- دوال معالجة النصوص

### 🌐 `src/services/`
الخدمات والاتصال مع APIs:
- `prayerTimesService`: خدمة جلب أوقات الصلاة من API الكفيل
- خدمات أخرى (قيد الإضافة)

### 🎣 `src/hooks/`
React Hooks مخصصة (قيد الإنشاء):
- `useLocalStorage`: للتعامل مع التخزين المحلي
- `usePrayerTimes`: لإدارة أوقات الصلاة
- `useTheme`: لإدارة المظاهر

### 🎨 `src/styles/`
ملفات التنسيق المخصصة (قيد الإنشاء):
- متغيرات CSS
- تنسيقات مخصصة
- مظاهر إضافية

### 📊 `src/data/`
البيانات الثابتة (قيد الإنشاء):
- بيانات المحافظات
- قوائم الإنجازات
- نصوص الترجمة

## 🔧 التقنيات المستخدمة

- **React 19**: مكتبة واجهة المستخدم
- **TypeScript**: لغة البرمجة مع الأنواع
- **Tailwind CSS**: إطار عمل التنسيق
- **Vite**: أداة البناء والتطوير
- **React Router**: للتنقل بين الصفحات
- **Heroicons**: مكتبة الأيقونات

## 🚀 كيفية التشغيل

```bash
# تثبيت الحزم
npm install

# تشغيل الخادم المحلي
npm run dev

# بناء المشروع للإنتاج
npm run build

# معاينة البناء
npm run preview
```

## 📝 ملاحظات التطوير

### 🎯 مبادئ التنظيم
1. **فصل الاهتمامات**: كل مجلد له غرض محدد
2. **إعادة الاستخدام**: المكونات قابلة للاستخدام المتعدد
3. **سهولة الصيانة**: هيكل واضح ومنطقي
4. **قابلية التوسع**: سهولة إضافة ميزات جديدة

### 🔄 تدفق البيانات
```
App.tsx → Components → Services → Utils → Constants
```

### 📦 إدارة الحالة
- **Local State**: للحالات المحلية في المكونات
- **Props**: لتمرير البيانات بين المكونات
- **Local Storage**: للبيانات المستمرة
- **Context API**: للحالات العامة (قيد الإضافة)

### 🎨 نظام التصميم
- **الألوان**: تدرجات خضراء إسلامية
- **الخطوط**: دعم العربية والإنجليزية
- **المظاهر**: فاتح ومظلم
- **الاستجابة**: تصميم متجاوب للجوال

## 🔮 الخطط المستقبلية

### 📱 المكونات المخططة
- [ ] مكونات UI أساسية (Button, Input, Modal)
- [ ] مكونات Layout متقدمة
- [ ] مكونات صفحات إضافية

### 🎣 Hooks مخصصة
- [ ] `useLocalStorage`
- [ ] `usePrayerTimes`
- [ ] `useTheme`
- [ ] `useAchievements`

### 🌐 خدمات إضافية
- [ ] خدمة الإشعارات
- [ ] خدمة الإنجازات
- [ ] خدمة النسخ الاحتياطي

### 🎨 تحسينات التصميم
- [ ] نظام ألوان متقدم
- [ ] رسوم متحركة
- [ ] مظاهر إضافية
- [ ] دعم RTL محسن

---

**📧 للاستفسارات والمساهمة**: يرجى فتح issue في المستودع أو التواصل مع فريق التطوير.
