// ===== أدوات مساعدة عامة =====

import { TAKLEF_AGES, STORAGE_KEYS } from '../constants';
import { UserData, PrayerTimesResponse, StorageKey } from '../types';

// ===== دوال التاريخ والوقت =====

/**
 * حساب سن التكليف حسب الجنس
 */
export function calculateTaklefAge(birthDate: Date, gender: 'male' | 'female'): Date {
  const taklefDate = new Date(birthDate);
  const ages = TAKLEF_AGES[gender];
  
  taklefDate.setFullYear(taklefDate.getFullYear() + ages.years);
  taklefDate.setMonth(taklefDate.getMonth() + ages.months);
  taklefDate.setDate(taklefDate.getDate() + ages.days);
  
  return taklefDate;
}

/**
 * تنسيق التاريخ للعرض
 */
export function formatDate(date: Date, language: 'ar' | 'en' = 'ar'): string {
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  };
  
  const locale = language === 'ar' ? 'ar-SA' : 'en-US';
  return date.toLocaleDateString(locale, options);
}

/**
 * تنسيق الوقت للعرض
 */
export function formatTime(time: string, language: 'ar' | 'en' = 'ar'): string {
  try {
    const [hours, minutes] = time.split(':');
    const date = new Date();
    date.setHours(parseInt(hours), parseInt(minutes));
    
    const options: Intl.DateTimeFormatOptions = {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    };
    
    const locale = language === 'ar' ? 'ar-SA' : 'en-US';
    return date.toLocaleTimeString(locale, options);
  } catch {
    return time;
  }
}

/**
 * الحصول على التاريخ الحالي بصيغة YYYY-MM-DD
 */
export function getCurrentDate(): string {
  return new Date().toISOString().split('T')[0];
}

/**
 * الحصول على اسم اليوم
 */
export function getDayName(date: Date, language: 'ar' | 'en' = 'ar'): string {
  const options: Intl.DateTimeFormatOptions = { weekday: 'long' };
  const locale = language === 'ar' ? 'ar-SA' : 'en-US';
  return date.toLocaleDateString(locale, options);
}

/**
 * الحصول على اسم الشهر
 */
export function getMonthName(date: Date, language: 'ar' | 'en' = 'ar'): string {
  const options: Intl.DateTimeFormatOptions = { month: 'long' };
  const locale = language === 'ar' ? 'ar-SA' : 'en-US';
  return date.toLocaleDateString(locale, options);
}

// ===== دوال التخزين المحلي =====

/**
 * حفظ البيانات في التخزين المحلي
 */
export function saveToLocalStorage<T>(key: StorageKey, data: T): void {
  try {
    const serializedData = JSON.stringify(data);
    localStorage.setItem(key, serializedData);
  } catch (error) {
    console.error('خطأ في حفظ البيانات:', error);
  }
}

/**
 * تحميل البيانات من التخزين المحلي
 */
export function loadFromLocalStorage<T>(key: StorageKey): T | null {
  try {
    const serializedData = localStorage.getItem(key);
    if (serializedData === null) {
      return null;
    }
    return JSON.parse(serializedData) as T;
  } catch (error) {
    console.error('خطأ في تحميل البيانات:', error);
    return null;
  }
}

/**
 * حذف البيانات من التخزين المحلي
 */
export function removeFromLocalStorage(key: StorageKey): void {
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.error('خطأ في حذف البيانات:', error);
  }
}

/**
 * مسح جميع بيانات التطبيق
 */
export function clearAllAppData(): void {
  Object.values(STORAGE_KEYS).forEach(key => {
    removeFromLocalStorage(key as StorageKey);
  });
}

// ===== دوال التحقق من صحة البيانات =====

/**
 * التحقق من صحة البريد الإلكتروني
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * التحقق من صحة التاريخ
 */
export function isValidDate(dateString: string): boolean {
  const date = new Date(dateString);
  return !isNaN(date.getTime()) && date <= new Date();
}

/**
 * التحقق من أن تاريخ بداية الصلاة بعد تاريخ الميلاد
 */
export function isValidPrayerStartDate(birthDate: string, prayerStartDate: string): boolean {
  const birth = new Date(birthDate);
  const prayerStart = new Date(prayerStartDate);
  return prayerStart >= birth;
}

// ===== دوال الحسابات =====

/**
 * حساب نسبة الإكمال
 */
export function calculateCompletionRate(completed: number, total: number): number {
  if (total === 0) return 0;
  return Math.round((completed / total) * 100);
}

/**
 * حساب عدد الأيام بين تاريخين
 */
export function daysBetween(date1: Date, date2: Date): number {
  const oneDay = 24 * 60 * 60 * 1000;
  return Math.round(Math.abs((date1.getTime() - date2.getTime()) / oneDay));
}

/**
 * التحقق من أن التاريخ هو اليوم
 */
export function isToday(date: Date): boolean {
  const today = new Date();
  return date.toDateString() === today.toDateString();
}

/**
 * التحقق من أن التاريخ في الماضي
 */
export function isPastDate(date: Date): boolean {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return date < today;
}

/**
 * التحقق من أن التاريخ في المستقبل
 */
export function isFutureDate(date: Date): boolean {
  const today = new Date();
  today.setHours(23, 59, 59, 999);
  return date > today;
}

// ===== دوال النصوص =====

/**
 * تحويل النص إلى أحرف كبيرة للحرف الأول
 */
export function capitalize(text: string): string {
  return text.charAt(0).toUpperCase() + text.slice(1);
}

/**
 * قطع النص إذا كان أطول من الحد المسموح
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
}

/**
 * إزالة المسافات الزائدة
 */
export function trimWhitespace(text: string): string {
  return text.trim().replace(/\s+/g, ' ');
}

// ===== دوال الألوان =====

/**
 * تحويل اللون من HEX إلى RGB
 */
export function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
}

/**
 * الحصول على لون حسب النسبة المئوية
 */
export function getColorByPercentage(percentage: number): string {
  if (percentage >= 90) return '#15803d'; // أخضر
  if (percentage >= 70) return '#22c55e'; // أخضر فاتح
  if (percentage >= 50) return '#f59e0b'; // أصفر
  return '#ef4444'; // أحمر
}

// ===== دوال الملفات =====

/**
 * تحميل ملف JSON
 */
export function downloadJSON(data: any, filename: string): void {
  const dataStr = JSON.stringify(data, null, 2);
  const dataBlob = new Blob([dataStr], { type: 'application/json' });
  const url = URL.createObjectURL(dataBlob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  URL.revokeObjectURL(url);
}

/**
 * قراءة ملف JSON
 */
export function readJSONFile(file: File): Promise<any> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target?.result as string);
        resolve(data);
      } catch (error) {
        reject(new Error('ملف JSON غير صحيح'));
      }
    };
    reader.onerror = () => reject(new Error('خطأ في قراءة الملف'));
    reader.readAsText(file);
  });
}

// ===== دوال أخرى =====

/**
 * تأخير التنفيذ
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * إنشاء معرف فريد
 */
export function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

/**
 * نسخ النص إلى الحافظة
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch {
    return false;
  }
}
